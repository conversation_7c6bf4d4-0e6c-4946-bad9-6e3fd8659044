import {
 readdirSync, statSync 
} from 'fs'
import {
 join, dirname 
} from 'path'
import {
 fileURLToPath, pathToFileURL 
} from 'url'

import { Collection } from 'discord.js'

import { logger } from '../logging/logger.js'

import type { Command } from './types.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

class CommandManager {
  private commands: Collection<string, Command>

  constructor() {
    this.commands = new Collection()
  }

  async loadCommands(): Promise<void> {
    try {
      const commandsPath = join(__dirname, '../../commands')
      const commandFolders = readdirSync(commandsPath)

      for (const folder of commandFolders) {
        const folderPath = join(commandsPath, folder)

        if (!statSync(folderPath).isDirectory()) {
          continue
        }

        const handlerPath = join(folderPath, 'handler.ts')

        try {
          statSync(handlerPath)

          const handlerUrl = pathToFileURL(handlerPath).href
          const commandModule = await import(handlerUrl)
          const command = commandModule.default as Command

          if (command && command.data && command.data.name) {
            this.commands.set(command.data.name, command)
            logger.debug(`Loaded command: ${command.data.name} from ${folder}`)
          } else {
            logger.warn(`Invalid command structure in ${folder}/handler.ts`)
          }
        } catch (error) {
          logger.warn(`Failed to load command from ${folder}:`, error)
        }
      }

      logger.info(`Successfully loaded ${this.commands.size} commands`)
    } catch (error) {
      logger.error(`Error loading commands:`, error)
      throw error
    }
  }

  getCommands(): Collection<string, Command> {
    return this.commands
  }

  getCommand(name: string): Command | undefined {
    return this.commands.get(name)
  }
}

export const commandManager = new CommandManager()
