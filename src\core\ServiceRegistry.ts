import type {
 InstanceConfig, InstanceMap
} from '../interfaces/config.js'
import type { ApiService } from '../services/api/api-service.js'
import type { SocketService } from '../services/socket/service.js'
import type {
 RoleStatus, RoleStatusMap, TitleToggleMap
} from '../services/title/types.js'
import { logger } from '../utils/logging/logger.js'

export interface ServiceInstances {
  api: ApiService
  socket?: SocketService
  instanceConfig: InstanceConfig
}

export class ServiceRegistry {
  private static instance: ServiceRegistry
  private services: InstanceMap<ServiceInstances> = {}
  private titleToggleMap: InstanceMap<TitleToggleMap> = {}
  private roleStatusMap: RoleStatusMap = {}

  private constructor() {}

  public static getInstance(): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      ServiceRegistry.instance = new ServiceRegistry()
    }
    return ServiceRegistry.instance
  }

  public registerServices(
    continent: number,
    api: ApiService,
    instanceConfig: InstanceConfig,
    socket?: SocketService
  ): void {
    this.services[continent] = {
      api,
      socket,
      instanceConfig
    }

    this.titleToggleMap[continent] = { [continent]: true }
    this.roleStatusMap[continent.toString()] = {
      hasRole: false,
      roleName: '',
      ueid: '',
      expirationTime: undefined
    }

    logger.debug(`[ServiceRegistry] Registered services for continent ${continent}`)
  }

  public getServices(continent: number): ServiceInstances {
    const services = this.services[continent]
    if (!services) {
      throw new Error(`Services not registered for continent ${continent}`)
    }
    return services
  }

  public getApiService(continent: number): ApiService {
    return this.getServices(continent).api
  }

  public getSocketService(continent: number): SocketService | undefined {
    return this.getServices(continent).socket
  }

  public getInstanceConfig(continent: number): InstanceConfig {
    return this.getServices(continent).instanceConfig
  }

  public hasServices(continent: number): boolean {
    return !!this.services[continent]
  }

  public hasSocketService(continent: number): boolean {
    return !!this.services[continent]?.socket
  }

  public getTitleToggleMap(continent: number): Record<string, boolean> {
    if (!this.titleToggleMap[continent]) {
      this.titleToggleMap[continent] = { [continent]: true }
    }
    return this.titleToggleMap[continent]
  }

  public setTitleToggle(continent: number, key: string, value: boolean): void {
    if (!this.titleToggleMap[continent]) {
      this.titleToggleMap[continent] = {}
    }
    this.titleToggleMap[continent][key] = value
  }

  public getRoleStatusMap(): RoleStatusMap {
    return this.roleStatusMap
  }

  public setRoleStatus(continent: number, roleStatus: RoleStatus): void {
    this.roleStatusMap[continent.toString()] = roleStatus
  }

  public unregisterServices(continent: number): void {
    delete this.services[continent]
    delete this.titleToggleMap[continent]
    delete this.roleStatusMap[continent.toString()]
    logger.debug(`[ServiceRegistry] Unregistered services for continent ${continent}`)
  }

  public getAllContinents(): number[] {
    return Object.keys(this.services).map(Number)
  }

  public clear(): void {
    this.services = {}
    this.titleToggleMap = {}
    this.roleStatusMap = {}
    logger.debug('[ServiceRegistry] Cleared all services')
  }
}

export const serviceRegistry = ServiceRegistry.getInstance()
