import { Collection } from 'discord.js'

import verifyCommand from '../../commands/verify/handler.js'
import { logger } from '../logging/logger.js'

import type { Command } from './types.js'

class CommandManager {
  private commands: Collection<string, Command>

  constructor() {
    this.commands = new Collection()
  }

  async loadCommands(): Promise<void> {
    try {
      const commands: Command[] = [
        verifyCommand
      ]

      for (const command of commands) {
        if (command && command.data && typeof command.data === 'object' && command.data !== null && 'name' in command.data && typeof command.data.name === 'string' && command.execute) {
          this.commands.set(command.data.name, command)
          logger.debug(`Loaded command: ${command.data.name}`)
        } else {
          logger.warn(`Invalid command structure - missing data.name or execute function`)
        }
      }

      logger.info(`Successfully loaded ${this.commands.size} commands`)
    } catch (error) {
      logger.error(`Error loading commands:`, error)
      throw error
    }
  }

  getCommands(): Collection<string, Command> {
    return this.commands
  }

  getCommand(name: string): Command | undefined {
    return this.commands.get(name)
  }
}

export const commandManager = new CommandManager()
