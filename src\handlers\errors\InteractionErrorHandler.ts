import type { ChatInputCommandInteraction } from 'discord.js'

import { BotError } from '../../utils/common/error.js'
import { logger } from '../../utils/logging/logger.js'

export class InteractionErrorHandler {
  public static async handleCommandError(
    interaction: ChatInputCommandInteraction,
    error: unknown,
    commandName?: string
  ): Promise<void> {
    const errorMessage = this.getErrorMessage(error)
    const userMessage = this.getUserFriendlyMessage(error)
    
    logger.error(`Command error in ${commandName || 'unknown command'}:`, {
      error: errorMessage,
      userId: interaction.user.id,
      guildId: interaction.guildId,
      channelId: interaction.channelId,
      commandName,
      stack: error instanceof Error ? error.stack : undefined
    })

    await this.sendErrorResponse(interaction, userMessage)
  }

  private static getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message
    }
    return String(error)
  }

  private static getUserFriendlyMessage(error: unknown): string {
    if (error instanceof BotError) {
      switch (error.code) {
        case 'LOGIN_ERROR':
          return '❌ Authentication failed. Please try again later.'
        case 'not_king':
          return '❌ Account does not have required permissions. Please try again later.'
        case 'INVALID_DATE_FORMAT':
          return '❌ Please use the format MM/dd/yyyy for dates.'
        case 'PERMISSION_DENIED':
          return '❌ You do not have permission to use this command.'
        case 'CHANNEL_RESTRICTION':
          return '❌ This command can only be used in the specified channel.'
        case 'ROLE_REQUIRED':
          return '❌ You need the required role to use this command.'
        default:
          return '❌ An error occurred while processing your request.'
      }
    }

    if (error instanceof Error) {
      if (error.message.includes('Missing Permissions')) {
        return '❌ The bot does not have the required permissions to perform this action.'
      }
      if (error.message.includes('Unknown interaction')) {
        return '❌ This interaction has expired. Please try the command again.'
      }
      if (error.message.includes('rate limit')) {
        return '❌ Rate limit exceeded. Please wait a moment and try again.'
      }
    }

    return '❌ An unexpected error occurred. Please try again later.'
  }

  private static async sendErrorResponse(
    interaction: ChatInputCommandInteraction,
    message: string
  ): Promise<void> {
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: message,
          ephemeral: true
        })
      } else if (interaction.deferred && !interaction.replied) {
        await interaction.editReply({
          content: message
        })
      } else {
        await interaction.followUp({
          content: message,
          ephemeral: true
        })
      }
    } catch (replyError) {
      logger.error('Failed to send error response to user:', {
        originalError: message,
        replyError: replyError instanceof Error ? replyError.message : String(replyError),
        interactionState: {
          replied: interaction.replied,
          deferred: interaction.deferred
        }
      })
    }
  }

  public static async handleInteractionError(
    interaction: ChatInputCommandInteraction,
    error: unknown
  ): Promise<void> {
    await this.handleCommandError(interaction, error, interaction.commandName)
  }

  public static createErrorWrapper<T extends unknown[]>(
    commandFunction: (...args: T) => Promise<void>,
    commandName: string
  ): (...args: T) => Promise<void> {
    return async (...args: T): Promise<void> => {
      try {
        await commandFunction(...args)
      } catch (error) {
        const interaction = args[0] as ChatInputCommandInteraction
        await this.handleCommandError(interaction, error, commandName)
      }
    }
  }
}
