import {
 type Interaction, InteractionType
} from 'discord.js'

import { logger } from '../../utils/logging/logger.js'
import { InteractionErrorHandler } from '../errors/InteractionErrorHandler.js'

import { handleButton } from './ButtonInteractions.js'
import { handleCommand } from './CommandInteractions.js'
import { handleSelectMenu } from './SelectMenuInteractions.js'

export type InteractionHandlerFunction = (
  interaction: Interaction
) => Promise<void>

const interactionHandlers = new Map<
  InteractionType,
  InteractionHandlerFunction
>([
  [
    InteractionType.ApplicationCommand,
    handleCommand as InteractionHandlerFunction
  ],
  [
    InteractionType.MessageComponent,
    async (interaction) => {
      if (interaction.isButton()) {
        return handleButton(interaction)
      }
      if (interaction.isStringSelectMenu()) {
        return handleSelectMenu(interaction)
      }
    }
  ]
])

export async function handleInteraction(
  interaction: Interaction
): Promise<void> {
  const handler = interactionHandlers.get(interaction.type)
  if (!handler) {
    logger.warn(`No handler found for interaction type: ${interaction.type}`)
    return
  }

  try {
    await handler(interaction)
  } catch (error) {
    if (interaction.isChatInputCommand()) {
      await InteractionErrorHandler.handleInteractionError(interaction, error)
    } else {
      logger.error(`Error handling ${interaction.type} interaction:`, error)
      if (
        interaction.isRepliable() &&
        !interaction.replied &&
        !interaction.deferred
      ) {
        try {
          await interaction.reply({
            content: 'An error occurred while processing your request.',
            ephemeral: true
          })
        } catch (replyError) {
          logger.error('Error sending error reply:', replyError)
        }
      }
    }
  }
}
