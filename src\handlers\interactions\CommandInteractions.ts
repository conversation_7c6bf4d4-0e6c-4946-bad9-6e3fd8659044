import type { ChatInputCommandInteraction } from 'discord.js'

import { ExecutionContext } from '../../core/ExecutionContext.js'
import { commandManager } from '../../utils/discord/command-manager.js'
import { logger } from '../../utils/logging/logger.js'
import { InteractionErrorHandler } from '../errors/InteractionErrorHandler.js'

export async function handleCommand(
  interaction: ChatInputCommandInteraction
): Promise<void> {
  const { commandName } = interaction
  const command = commandManager.getCommand(commandName)

  if (!command) {
    logger.warn(`Command not found: ${commandName}`)
    await interaction.reply({
      content: 'Command not found',
      ephemeral: true
    })
    return
  }

  try {
    const context = new ExecutionContext(interaction)
    await command.execute(context)
  } catch (error) {
    await InteractionErrorHandler.handleCommandError(
      interaction,
      error,
      commandName
    )
  }
}
