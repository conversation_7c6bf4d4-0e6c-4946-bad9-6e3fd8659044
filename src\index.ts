import {
 Client, GatewayIntentBits, type Interaction
} from 'discord.js'

import { serviceRegistry } from './core/ServiceRegistry.js'
import { deployCommands } from './deploy-commands.js'
import { handleInteraction } from './handlers/interactions/InteractionHandler.js'
import { getApiInstance } from './services/api/api-instance.js'
import { initializeClient } from './services/client/client-init.js'
import { loadConfig } from './utils/config/config-loader.js'
import { commandManager } from './utils/discord/command-manager.js'
import { logger } from './utils/logging/logger.js'

logger.level = process.env.NODE_ENV === 'production' ? 'info' : 'debug'

async function main() {
  try {
    const botConfig = await loadConfig()
    const { bot_token, instances } = botConfig

    let loginSuccessful = false

    for (const instance of instances) {
      try {
        const result = await initializeClient(instance)

        if (result.success) {
          loginSuccessful = true

          const apiInstance = getApiInstance(instance.continent)

          serviceRegistry.registerServices(
            instance.continent,
            apiInstance,
            instance
          )

          logger.info(
            `Successfully initialized client for Continent ${instance.continent}`
          )
        } else {
          logger.error(
            `Failed to initialize client for Continent ${instance.continent}: ${result.error?.message}`
          )
        }
      } catch (error) {
        logger.error(
          `Error initializing client for Continent ${instance.continent}:`,
          error
        )
      }
    }

    if (!loginSuccessful) {
      throw new Error('Failed to initialize any client')
    }

    logger.info('Loading commands...')
    await commandManager.loadCommands()

    await deployCommands(botConfig)

    const client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
      ]
    })

    client.on('interactionCreate', async (interaction: Interaction) => {
      if (
        !interaction.isCommand() &&
        !interaction.isButton() &&
        !interaction.isStringSelectMenu()
      )
        return

      if (!interaction.guildId) {
        logger.warn('Interaction received without guild ID')
        return
      }

      const instance = instances.find(
        instance => instance.guild_id === interaction.guildId
      )
      if (!instance) {
        logger.warn(`No instance config found for guild ${interaction.guildId}`)
        return
      }

      if (!serviceRegistry.hasServices(instance.continent)) {
        logger.warn(`No services registered for continent ${instance.continent}`)
        return
      }

      const services = serviceRegistry.getServices(instance.continent)
      const titleToggleMap = serviceRegistry.getTitleToggleMap(instance.continent)
      const roleStatusMap = serviceRegistry.getRoleStatusMap()

      await handleInteraction(
        interaction,
        services.instanceConfig,
        services.api,
        titleToggleMap,
        roleStatusMap,
        services.socket || null
      )
    })

    process.on('SIGINT', async () => {
      logger.info('Received SIGINT. Shutting down...')
      process.exit(0)
    })

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM. Shutting down...')
      process.exit(0)
    })

    await client.login(bot_token)
    logger.info('Bot is now online!')
  } catch (error) {
    logger.error('Fatal error:', { error })
    process.exit(1)
  }
}

main()
