import type { ExecutionContext } from '../../core/ExecutionContext.js'
import { addRoleToMember } from '../../services/role/role-service.js'
import { verifyUser } from '../../services/verify/verify-service.js'

async function execute(context: ExecutionContext): Promise<void> {
  try {
    context.logger.debug('Starting verify command execution')

    if (!(await context.verifyCommandChannel('verify'))) {
      context.logger.debug('Command channel verification failed')
      return
    }

    context.logger.debug('Deferring reply')
    await context.deferReply(false)

    const kingdomId = context.interaction.options.getString('uid', true)
    context.logger.debug(`Kingdom ID: ${kingdomId}`)

    context.logger.debug('Ensuring token')
    await context.ensureToken()

    context.logger.debug('Calling verifyUser service')
    const playerProfile = await verifyUser(
      context.instanceConfig,
      kingdomId,
      context.interaction.user.id
    )

    context.logger.debug(`Player profile result: ${JSON.stringify(playerProfile)}`)
    await context.editReply(playerProfile.message)

    if (playerProfile.isVerified) {
      const member = await context.interaction.guild?.members.fetch(
        context.interaction.user.id
      )
      if (!member) {
        throw new Error('Failed to fetch member')
      }

      const role = context.interaction.guild?.roles.cache.find(
        role => role.name === context.instanceConfig.verify.roleName
      )
      if (!role) {
        throw new Error('Verification role not found')
      }

      if (member.roles.cache.has(role.id)) {
        context.logger.info(`Member already has verification role`, {
          memberName: member.displayName,
          roleName: role.name
        })
        await context.followUp(
          `You already have the '${role.name}' role.`,
          false
        )
      } else {
        await addRoleToMember(member, role)
        await context.followUp(
          `Role '${role.name}' has been added to ${member.displayName}.`,
          false
        )
        context.logger.info(`Verification role added to member`, {
          memberName: member.displayName,
          roleName: role.name
        })
      }
    }
  } catch (error) {
    context.logger.error('Error in verify command execute function:', error)
    throw error
  }
}

export default {
  data: {
    name: 'verify',
    description: 'Verify your account and receive a role',
    options: [
      {
        name: 'uid',
        description: 'Your kingdom ID',
        type: 3,
        required: true
      }
    ]
  },
  execute
}
