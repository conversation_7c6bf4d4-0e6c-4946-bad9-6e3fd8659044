import { setSettings } from "utils/config/config-utils.js"

import type { InstanceConfig } from "../../interfaces/config.js"
import { BotError } from "../../utils/common/error.js"
import { logger } from "../../utils/logging/logger.js"
import { login } from "../auth/login-service.js"
import { enterKingdom } from "../kingdom/kingdom-service.js"
import { initializeSocketService } from "../socket/connection/instance.js"
import type { SocketService } from "../socket/service.js"

/**
 * Initialize client for a specific instance
 * This handles the proper sequence of:
 * 1. Login
 * 2. Call kingdom/enter API to get network URLs & STORE w/. SET SETTINGS!!
 * 3. Establish socket connections in order: socc, sock, socf
 */
export async function initializeClient(instanceConfig: InstanceConfig): Promise<{
  success: boolean
  socketService?: SocketService
  error?: Error
}> {
  const continent = instanceConfig.continent

  try {
    logger.info(`[ClientInit] Logging in for Continent ${continent}`)
    const loginResult = await login(instanceConfig)

    if (!loginResult || !loginResult.token || !loginResult.regionHash) {
      throw new BotError("Login failed or missing token/regionHash", "LOGIN_FAILED")
    }

    logger.info(`[ClientInit] Successfully logged in for Continent ${continent}`)
    logger.info(`[ClientInit] Entering kingdom for Continent ${continent}`)

    const kingdomResponse = await enterKingdom(instanceConfig)
    setSettings(instanceConfig, "kingdomData", kingdomResponse)

    logger.info(`[ClientInit] Successfully entered kingdom for Continent ${continent}`)

    // Initialize socket service
    logger.info(`[ClientInit] Initializing socket service for Continent ${continent}`)
    const socketService = initializeSocketService(instanceConfig)

    try {
      // Initialize socket connections (chats, kingdoms, fields)
      await socketService.initialize(["socc", "sock"])
      logger.info(`[ClientInit] Socket service initialized for Continent ${continent}`)
      return { success: true, socketService }
    } catch (socketError) {
      logger.error(`[ClientInit] Failed to initialize socket service for Continent ${continent}:`, socketError)
      return { success: false }
    }
  } catch (error) {
    logger.error(`[ClientInit] Failed to initialize client for Continent ${continent}:`, error)
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
    }
  }
}

// Re-export socket service functions
//export const getSocketService = getWsService;
//export const hasSocketService = hasWsService;
//export const closeAllSocketServices = closeAllWsServices;

