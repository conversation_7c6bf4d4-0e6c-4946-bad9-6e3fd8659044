import type { ChatInputCommandInteraction } from 'discord.js'

import type { InstanceConfig } from '../interfaces/config.js'
import { updateParams } from '../services/api/api-instance.js'
import type { ApiService } from '../services/api/api-service.js'
import { login } from '../services/auth/login-service.js'
import type { SocketService } from '../services/socket/service.js'
import type { RoleStatus, RoleStatusMap } from '../services/title/types.js'
import { BotError } from '../utils/common/error.js'
import { decodeRegionHash } from '../utils/decode.js'
import { CommandUtils } from '../utils/discord/command-utils.js'
import { verifyCommandChannel } from '../utils/discord/command-utils.js'
import { MessageUtils } from '../utils/discord/message-utils.js'
import { logger } from '../utils/logging/logger.js'

import { serviceRegistry } from './ServiceRegistry.js'

export class ExecutionContext {
  public readonly interaction: ChatInputCommandInteraction
  public readonly instanceConfig: InstanceConfig
  public readonly apiService: ApiService
  public readonly socketService?: SocketService
  public readonly continent: number
  public readonly logger = logger
  public readonly messageUtils: MessageUtils

  constructor(interaction: ChatInputCommandInteraction) {
    this.interaction = interaction

    this.continent = this.getContinentFromGuild(interaction.guildId!)
    
    const services = serviceRegistry.getServices(this.continent)
    this.instanceConfig = services.instanceConfig
    this.apiService = services.api
    this.socketService = services.socket

    this.messageUtils = new MessageUtils()
  }

  private getContinentFromGuild(guildId: string): number {
    const continents = serviceRegistry.getAllContinents()
    for (const continent of continents) {
      const config = serviceRegistry.getInstanceConfig(continent)
      if (config.guild_id === guildId) {
        return continent
      }
    }
    throw new Error(`No continent found for guild ID: ${guildId}`)
  }

  public async ensureToken(): Promise<void> {
    if (!this.instanceConfig.token) {
      await this.refreshToken()
    }
  }

  public async refreshToken(): Promise<void> {
    const result = await login(this.instanceConfig)
    if (!result) {
      throw new BotError('Login failed', 'LOGIN_ERROR')
    }
    this.instanceConfig.token = result.token
    this.instanceConfig.xorKey = decodeRegionHash(result.regionHash)
    updateParams(
      this.instanceConfig.token,
      this.instanceConfig.xorKey,
      this.instanceConfig
    )
    this.logger.debug('Token refreshed in ExecutionContext', {
      continent: this.continent
    })
  }

  public async validateRole(roleName: string): Promise<boolean> {
    return CommandUtils.validateRole(this.interaction, roleName)
  }

  public async verifyCommandChannel(commandName: string): Promise<boolean> {
    return verifyCommandChannel(
      this.interaction,
      this.instanceConfig,
      commandName
    )
  }

  public getTitleToggleMap(): Record<string, boolean> {
    return serviceRegistry.getTitleToggleMap(this.continent)
  }

  public setTitleToggle(key: string, value: boolean): void {
    serviceRegistry.setTitleToggle(this.continent, key, value)
  }

  public getRoleStatusMap(): RoleStatusMap {
    return serviceRegistry.getRoleStatusMap()
  }

  public setRoleStatus(roleStatus: RoleStatus): void {
    serviceRegistry.setRoleStatus(this.continent, roleStatus)
  }

  public async handleError(error: unknown, customMessage: string): Promise<void> {
    this.logger.error(`Error in command execution:`, error)

    try {
      if (!this.interaction.replied && !this.interaction.deferred) {
        await this.interaction.reply({
          content: customMessage || 'An error occurred while executing this command.',
          ephemeral: true
        })
      } else if (!this.interaction.replied) {
        await this.interaction.editReply({
          content: customMessage || 'An error occurred while executing this command.'
        })
      }
    } catch (replyError) {
      this.logger.error('Failed to send error response:', replyError)
    }
  }

  public async deferReply(ephemeral = true): Promise<void> {
    await this.interaction.deferReply({ ephemeral })
  }

  public async reply(content: string, ephemeral = true): Promise<void> {
    await this.interaction.reply({ content, ephemeral })
  }

  public async editReply(content: string): Promise<void> {
    await this.interaction.editReply({ content })
  }

  public async followUp(content: string, ephemeral = true): Promise<void> {
    await this.interaction.followUp({ content, ephemeral })
  }
}
