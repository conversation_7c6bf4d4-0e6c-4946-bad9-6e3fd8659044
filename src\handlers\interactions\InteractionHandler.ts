import type { Interaction } from 'discord.js'
import { SocketService } from 'services/socket/service.js'
import type {
 RoleStatusMap, TitleToggleMap
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'

import { logger } from '../../utils/logging/logger.js'
import { InteractionErrorHandler } from '../errors/InteractionErrorHandler.js'

import { handleButton } from './ButtonInteractions.js'
import { handleCommand } from './CommandInteractions.js'
import { handleSelectMenu } from './SelectMenuInteractions.js'



export async function handleInteraction(
  interaction: Interaction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService: SocketService | null
): Promise<void> {
  try {
    if (interaction.isChatInputCommand()) {
      await handleCommand(interaction)
    } else if (interaction.isButton()) {
      await handleButton(
        interaction,
        instanceConfig,
        apiInstance,
        titleToggleMap,
        roleStatusMap,
        socketService
      )
    } else if (interaction.isStringSelectMenu()) {
      await handleSelectMenu(
        interaction,
        instanceConfig,
        apiInstance,
        titleToggleMap,
        roleStatusMap,
        socketService
      )
    } else {
      logger.warn(`No handler found for interaction type: ${interaction.type}`)
    }
  } catch (error) {
    if (interaction.isChatInputCommand()) {
      await InteractionErrorHandler.handleInteractionError(interaction, error)
    } else {
      logger.error(`Error handling ${interaction.type} interaction:`, error)
      if (
        interaction.isRepliable() &&
        !interaction.replied &&
        !interaction.deferred
      ) {
        try {
          await interaction.reply({
            content: 'An error occurred while processing your request.',
            ephemeral: true
          })
        } catch (replyError) {
          logger.error('Error sending error reply:', replyError)
        }
      }
    }
  }
}
